"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

// TypeScript interface for the API response
interface TestOrderResponse {
  success?: boolean;
  message?: string;
  orderId?: string;
  product?: string;
  variant?: string;
  error?: string;
}

export default function TestOrderPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<TestOrderResponse | null>(null);

  const createTestOrder = async () => {
    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/admin/create-test-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error('Error creating test order:', error);
      setResult({
        error: error instanceof Error ? error.message : 'Failed to create test order'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6 p-6">
      <h1 className="text-2xl font-semibold">Create Test Order</h1>
      
      <Card className="p-6">
        <div className="space-y-4">
          <p className="text-muted-foreground">
            This will create a test order with variant information (Cropped Zip Up Hoodie - Rustic Black, Medium)
            to test the admin orders page display.
          </p>
          
          <Button 
            onClick={createTestOrder} 
            disabled={loading}
            className="w-full"
          >
            {loading ? 'Creating Test Order...' : 'Create Test Order'}
          </Button>
          
          {result && (
            <div className="mt-4 p-4 border rounded-md">
              <pre className="text-sm">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
