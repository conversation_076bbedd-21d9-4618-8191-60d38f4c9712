{"name": "revision", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-slot": "^1.1.2", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.29.3", "lucide-react": "^0.479.0", "next": "^15.3.2", "nuqs": "^2.4.1", "postgres": "^3.4.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "shadcn-ui": "^0.9.5", "square": "^42.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.20.14", "eslint": "^9", "eslint-config-next": "15.2.2", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5"}}