import * as dotenv from 'dotenv';
import { resolve } from 'path';

// Load environment variables
dotenv.config({ path: resolve(__dirname, '../.env.local') });

import { db } from '../src/db/index';
import { orders, orderItems, products, productVariants, colors, sizes } from '../src/db/schema';
import { eq, and } from 'drizzle-orm';

async function createTestOrder() {
  try {
    console.log('Creating test order with variant information...');
    
    // First, find the cropped hoodie product
    const [product] = await db
      .select()
      .from(products)
      .where(eq(products.name, 'Cropped Zip Up Hoodie'))
      .limit(1);

    if (!product) {
      console.log('❌ Cropped Zip Up Hoodie product not found. Please run the seed script first.');
      process.exit(1);
    }

    console.log('✅ Found product:', product.name);

    // Find a specific variant (Rustic Black, Medium)
    const variant = await db
      .select({
        variantId: productVariants.id,
        productId: productVariants.productId,
        colorName: colors.name,
        sizeName: sizes.name,
        price: productVariants.price,
      })
      .from(productVariants)
      .leftJoin(colors, eq(productVariants.colorId, colors.id))
      .leftJoin(sizes, eq(productVariants.sizeId, sizes.id))
      .where(
        and(
          eq(productVariants.productId, product.id),
          eq(colors.name, 'Rustic Black'),
          eq(sizes.name, 'Medium')
        )
      )
      .limit(1);

    if (variant.length === 0) {
      console.log('❌ Variant not found. Please run the seed script first.');
      process.exit(1);
    }

    console.log('✅ Found variant:', variant[0].colorName, variant[0].sizeName);

    // Create a test order
    const [newOrder] = await db.insert(orders).values({
      userId: 'test-user-123',
      email: '<EMAIL>',
      subtotal: '45.00',
      shippingTotal: '5.99',
      discountTotal: '0.00',
      total: '50.99',
      status: 'pending',
      shippingAddress: JSON.stringify({
        street: '123 Test Street',
        city: 'Test City',
        state: 'CA',
        zip: '12345',
        country: 'US'
      }),
      billingAddress: JSON.stringify({
        street: '123 Test Street',
        city: 'Test City',
        state: 'CA',
        zip: '12345',
        country: 'US'
      }),
      paymentIntentId: 'test_payment_intent_123',
    }).returning();

    console.log('✅ Created order:', newOrder.id);

    // Create order item with variant information
    await db.insert(orderItems).values({
      orderId: newOrder.id,
      productId: product.id,
      variantId: variant[0].variantId,
      name: 'Cropped Zip Up Hoodie',
      description: 'A stylish cropped zip up hoodie',
      price: '45.00',
      quantity: 1,
      subtotal: '45.00',
    });

    console.log('✅ Created order item with variant information');
    console.log('🎉 Test order created successfully!');
    console.log('Order ID:', newOrder.id);
    console.log('Product:', product.name);
    console.log('Variant:', variant[0].colorName, variant[0].sizeName);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error creating test order:', error);
    process.exit(1);
  }
}

createTestOrder();
